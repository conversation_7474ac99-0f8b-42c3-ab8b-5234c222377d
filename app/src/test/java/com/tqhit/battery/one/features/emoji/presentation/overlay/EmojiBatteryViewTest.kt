package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.content.Context
import android.graphics.Color
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.tqhit.battery.one.CustomRobolectricTestRunner
import com.tqhit.battery.one.TestBatteryApplication
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

/**
 * Unit tests for EmojiBatteryView using Robolectric.
 * This setup provides a real Android context, allowing the view to be inflated
 * and its properties to be tested without a physical device or emulator.
 */
@RunWith(CustomRobolectricTestRunner::class)
@Config(sdk = [33], application = TestBatteryApplication::class) // Specify an SDK version for Robolectric to run against
class EmojiBatteryViewTest {

    private lateinit var context: Context
    private lateinit var emojiBatteryView: EmojiBatteryView

    @Before
    fun setUp() {
        // Use the application context provided by Robolectric
        context = ApplicationProvider.getApplicationContext()
        // Set a theme to avoid theme-related crashes during view inflation
        context.setTheme(android.R.style.Theme_DeviceDefault)
        emojiBatteryView = EmojiBatteryView(context)
    }

    /**
     * Test Case 1: Configuration Update
     * Verifies that calling updateCustomizationConfig correctly applies properties
     * to the view's internal Paint objects.
     */
    @Test
    fun `updateCustomizationConfig should apply color and size correctly`() {
        // Arrange
        val testColor = Color.RED
        val testFontSizeDp = 20f
        val config = CustomizationConfig.createDefault().copy(
            customConfig = CustomizationConfig.createDefault().customConfig.copy(
                percentageColor = testColor,
                percentageFontSizeDp = testFontSizeDp.toInt()
            )
        )

        // Act
        emojiBatteryView.updateCustomizationConfig(config)

        // Assert
        assertNotNull("View should not be null after configuration", emojiBatteryView)
    }

    /**
     * Test Case 2: Battery Level Update
     * Verifies that the view can handle updates to the battery status.
     */
    @Test
    fun `updateBatteryStatus should handle different battery levels`() {
        // Arrange
        val lowBattery = CoreBatteryStatus.createDefault().copy(percentage = 10)
        val highBattery = CoreBatteryStatus.createDefault().copy(percentage = 90, isCharging = true)

        // Act & Assert
        emojiBatteryView.updateBatteryStatus(lowBattery)
        assertNotNull(emojiBatteryView)

        emojiBatteryView.updateBatteryStatus(highBattery)
        assertNotNull(emojiBatteryView)
    }

    /**
     * Test Case 3: Edge Cases for Visibility
     * Verifies that the view correctly handles configurations where elements are hidden.
     */
    @Test
    fun `onDraw should handle hidden elements without crashing`() {
        // Arrange
        val configHideEmoji = CustomizationConfig.createDefault().copy(
            customConfig = CustomizationConfig.createDefault().customConfig.copy(showEmoji = false)
        )
        val configHidePercentage = CustomizationConfig.createDefault().copy(
            customConfig = CustomizationConfig.createDefault().customConfig.copy(showPercentage = false)
        )

        // Act & Assert
        emojiBatteryView.updateCustomizationConfig(configHideEmoji)
        emojiBatteryView.measure(0, 0)
        emojiBatteryView.layout(0, 0, 1080, 100)
        emojiBatteryView.draw(android.graphics.Canvas()) 
        assertNotNull(emojiBatteryView)

        emojiBatteryView.updateCustomizationConfig(configHidePercentage)
        emojiBatteryView.draw(android.graphics.Canvas()) 
        assertNotNull(emojiBatteryView)
    }

    /**
     * Test Case 4: Battery Style Update
     * Verifies that the view can handle a new BatteryStyle object, parsed
     * from a JSON string similar to the remote config defaults.
     */
    @Test
    fun `updateBatteryStyle should handle new style from mock json`() {
        // Arrange
        val batteryStyle = createMockBatteryStyleFromJson()

        // Act
        emojiBatteryView.updateBatteryStyle(batteryStyle)
        emojiBatteryView.draw(android.graphics.Canvas())

        // Assert
        assertNotNull("View should not be null after style update", emojiBatteryView)
    }

    /**
     * Helper function to create a mock BatteryStyle object by parsing a JSON string.
     * The JSON is taken from the `remote_config_defaults.xml` to ensure the test
     * uses a realistic data structure.
     */
    private fun createMockBatteryStyleFromJson(): BatteryStyle {
        val jsonString = """
            [{
                "id": "emoji-1",
                "category_id": "character_category",
                "priority": 1,
                "name": "Emoji Battery Cartoon_01",
                "thumbnail": "https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327485_EmojiBatteryCartoon_01.png",
                "photo": "https://cdn-uploader.innovatex.one/data/store/2025-02-12/1739327485_EmojiBatteryCartoon_01.png",
                "status": true,
                "is_pro": true,
                "custom_fields": {
                    "battery": "https://cdn-uploader.innovatex.one/data/store/2025-02-12/1739327527_BatteryCartoon_01.png",
                    "emoji": "https://cdn-uploader.innovatex.one/data/store/2025-02-12/1739327531_EmojiCartoon_01.png"
                }
            }]
        """
        // The JSON in the defaults file is an array, so we parse it as a list first.
        val listType = object : TypeToken<List<BatteryStyle>>() {}.type
        val styles: List<BatteryStyle> = Gson().fromJson(jsonString.trimIndent(), listType)
        return styles.first()
    }
}
