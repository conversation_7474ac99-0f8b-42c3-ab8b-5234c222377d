package com.tqhit.battery.one

import com.tqhit.battery.one.utils.BatteryLogger
import io.mockk.mockk
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * A custom test runner that specifies the test application class to be used.
 * This runner instructs Robolectric to use our [TestBatteryApplication] for all tests,
 * which prevents the real application's Firebase initialization from running.
 * It also mocks the BatteryLogger to prevent it from initializing Firebase Crashlytics.
 */
class CustomRobolectricTestRunner(testClass: Class<*>) : RobolectricTestRunner(testClass) {
    override fun buildGlobalConfig(): Config {
        // Mock the BatteryLogger before any tests run
        mockk<BatteryLogger>(relaxed = true)

        return Config.Builder()
            .setApplication(TestBatteryApplication::class.java)
            .build()
    }
}
