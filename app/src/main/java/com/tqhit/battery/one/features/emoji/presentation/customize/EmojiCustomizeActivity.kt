package com.tqhit.battery.one.features.emoji.presentation.customize

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.MenuItem
import android.widget.SeekBar
import androidx.activity.viewModels
import androidx.activity.OnBackPressedCallback
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.tqhit.battery.one.R
import com.tqhit.battery.one.base.LocaleAwareActivity
import com.tqhit.battery.one.databinding.ActivityEmojiCustomizeBinding
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter
import com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryComponentAdapter
import com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiComponentAdapter
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiOverlayPermissionManager
import com.tqhit.battery.one.manager.theme.ThemeManager
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject
import android.graphics.drawable.Drawable
import com.bumptech.glide.load.DataSource

/**
 * Standalone Activity for customizing emoji battery styles.
 * Provides live preview and customization options for selected battery styles.
 * 
 * This Activity follows the established patterns in the app:
 * - Extends LocaleAwareActivity for proper locale support
 * - Uses ViewBinding for view access
 * - Implements MVI pattern with ViewModel
 * - Uses Hilt for dependency injection
 * - Follows Material 3 design guidelines
 * - Includes proper ActionBar/Toolbar with back navigation
 * - Adapts to current app theme (dark/light mode)
 */
@AndroidEntryPoint
class EmojiCustomizeActivity : LocaleAwareActivity<ActivityEmojiCustomizeBinding>() {
    
    companion object {
        private const val TAG = "EmojiCustomizeActivity"
        private const val EXTRA_BATTERY_STYLE = "battery_style"
        
        /**
         * Creates an intent to launch EmojiCustomizeActivity with a battery style.
         */
        fun createIntent(context: Context, batteryStyle: BatteryStyle): Intent {
            return Intent(context, EmojiCustomizeActivity::class.java).apply {
                putExtra(EXTRA_BATTERY_STYLE, batteryStyle)
            }
        }
    }
    
    // ViewBinding
    override val binding by lazy { ActivityEmojiCustomizeBinding.inflate(layoutInflater) }
    
    // ViewModel
    private val viewModel: CustomizeViewModel by viewModels()
    
    // Dependencies
    @Inject
    override lateinit var appRepository: AppRepository
    
    // Adapters - Phase 1 specialized adapters for component-specific display
    private lateinit var batteryComponentAdapter: BatteryComponentAdapter
    private lateinit var emojiComponentAdapter: EmojiComponentAdapter
    
    // State
    private var initialBatteryStyle: BatteryStyle? = null
    
    // Modern back handling
    private val backPressedCallback = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            Log.d(TAG, "EMOJI_NAVIGATION: OnBackPressedCallback triggered at ${System.currentTimeMillis()}")
            handleBackNavigation()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        // CRITICAL: Extract intent data FIRST, before calling super.onCreate()
        // This ensures the data is available when setupData() is called from the base class
        initialBatteryStyle = intent.getSerializableExtra(EXTRA_BATTERY_STYLE) as? BatteryStyle
        Log.d(TAG, "EmojiCustomizeActivity created with style: ${initialBatteryStyle?.name}")

        super.onCreate(savedInstanceState)

        // Apply theme before setting content view
        ThemeManager.applyTheme(this)

        // Set content view
        setContentView(binding.root)

        // Setup modern back handling for API 33+
        setupBackHandling()

        // Setup UI
        setupActionBar()
        setupUI()
    }
    
    override fun setupData() {
        super.setupData()
        Log.d(TAG, "EmojiCustomizeActivity setupData called")

        setupUIComponents()
        setupRecyclerViews()
        setupClickListeners()
        setupSliders()
        observeViewModel()

        // Initialize with the provided battery style or load initial data
        initialBatteryStyle?.let { style ->
            viewModel.handleEvent(CustomizeEvent.InitializeWithStyle(style))
        } ?: run {
            viewModel.handleEvent(CustomizeEvent.LoadInitialData)
        }
    }
    
    override fun onResume() {
        super.onResume()
        viewModel.handleEvent(CustomizeEvent.OnResume)

        // Check if permissions were granted while away from the app
        if (EmojiOverlayPermissionManager.hasAllRequiredPermissions(this)) {
            Log.d(TAG, "EMOJI_PERMISSION: Permissions now granted in Activity, enabling feature")
            viewModel.handlePermissionResult(granted = true)
        } else {
            Log.d(TAG, "EMOJI_PERMISSION: Permissions still missing in Activity")
            // Permission status will be logged by the permission manager
            val permissionStatus = EmojiOverlayPermissionManager.getPermissionStatusSummary(this)
            Log.d(TAG, "EMOJI_PERMISSION: Current permission status: $permissionStatus")
        }
    }
    
    override fun onPause() {
        super.onPause()
        viewModel.handleEvent(CustomizeEvent.OnPause)
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        Log.d(TAG, "EMOJI_NAVIGATION: onOptionsItemSelected called with itemId: ${item.itemId}")
        return when (item.itemId) {
            android.R.id.home -> {
                Log.d(TAG, "EMOJI_NAVIGATION: Back navigation clicked from toolbar at ${System.currentTimeMillis()}")
                handleBackNavigation()
                Log.d(TAG, "EMOJI_NAVIGATION: Returning true from onOptionsItemSelected")
                true
            }
            else -> {
                Log.d(TAG, "EMOJI_NAVIGATION: Delegating to super.onOptionsItemSelected for itemId: ${item.itemId}")
                super.onOptionsItemSelected(item)
            }
        }
    }
    
    override fun onBackPressed() {
        Log.d(TAG, "EMOJI_NAVIGATION: onBackPressed() called at ${System.currentTimeMillis()}")
        
        // For API 33+, the OnBackPressedCallback should handle this
        // For older APIs, handle directly
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Modern Android - OnBackPressedCallback should be triggered
            Log.d(TAG, "EMOJI_NAVIGATION: Modern Android (API 33+), delegating to OnBackPressedCallback")
            super.onBackPressed() // This triggers the registered callback
        } else {
            // Legacy Android - handle directly
            Log.d(TAG, "EMOJI_NAVIGATION: Legacy Android (API < 33), handling directly")
            handleBackNavigation()
        }
        
        Log.d(TAG, "EMOJI_NAVIGATION: onBackPressed() completed")
    }
    
    /**
     * Sets up modern back handling for API 33+ and compatibility for older versions
     */
    private fun setupBackHandling() {
        Log.d(TAG, "EMOJI_NAVIGATION: Setting up back handling for API ${Build.VERSION.SDK_INT}")
        
        // Register OnBackPressedCallback for all API levels
        // This provides consistent behavior and modern gesture support
        onBackPressedDispatcher.addCallback(this, backPressedCallback)
        
        Log.d(TAG, "EMOJI_NAVIGATION: OnBackPressedCallback registered successfully")
    }

    /**
     * Centralized back navigation handling with immediate finish
     */
    private fun handleBackNavigation() {
        Log.d(TAG, "EMOJI_NAVIGATION: handleBackNavigation called, isFinishing: $isFinishing")
        
        if (!isFinishing) {
            Log.d(TAG, "EMOJI_NAVIGATION: Finishing activity immediately")
            // Clear ViewModel state for consistency but don't wait for it
            viewModel.handleEvent(CustomizeEvent.NavigateBack)
            // Finish immediately to prevent double-tap issues
            finish()
            Log.d(TAG, "EMOJI_NAVIGATION: Activity.finish() called")
        } else {
            Log.d(TAG, "EMOJI_NAVIGATION: Activity already finishing, skipping navigation")
        }
    }
    
    /**
     * Sets up the ActionBar/Toolbar with proper theme integration and properly sized back arrow
     */
    private fun setupActionBar() {
        Log.d(TAG, "EMOJI_NAVIGATION: Setting up ActionBar")
        
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.customize_emoji_battery)
            Log.d(TAG, "EMOJI_NAVIGATION: ActionBar configured with home button enabled")
        }
        
        // Apply theme-aware colors to toolbar
        val currentTheme = ThemeManager.getSelectedTheme()
        val isDarkTheme = when (currentTheme) {
            "AutoTheme" -> {
                val mode = resources.configuration.uiMode and 
                    android.content.res.Configuration.UI_MODE_NIGHT_MASK
                mode == android.content.res.Configuration.UI_MODE_NIGHT_YES
            }
            "BlackTheme", "BlackThemeInverted", "AmoledTheme", "AmoledThemeInverted" -> true
            else -> false
        }
        
        // Apply theme-aware color to the ActionBar-controlled navigation icon
        try {
            val upArrow = androidx.appcompat.content.res.AppCompatResources.getDrawable(
                this,
                androidx.appcompat.R.drawable.abc_ic_ab_back_material
            )
            upArrow?.setTint(
                if (isDarkTheme) {
                    getColor(android.R.color.white)
                } else {
                    getColor(android.R.color.black)
                }
            )
            supportActionBar?.setHomeAsUpIndicator(upArrow)
            Log.d(TAG, "EMOJI_NAVIGATION: Set theme-aware ActionBar navigation icon")
        } catch (exception: Exception) {
            Log.e(TAG, "EMOJI_NAVIGATION: Error setting ActionBar navigation icon", exception)
        }
        
        Log.d(TAG, "EMOJI_NAVIGATION: ActionBar setup completed, navigation handled by onOptionsItemSelected")
    }
    
    /**
     * Sets up UI components and their initial states
     */
    private fun setupUIComponents() {
        Log.d(TAG, "Setting up UI components")

        // Initialize specialized adapters for Phase 1 component-specific display
        batteryComponentAdapter = BatteryComponentAdapter(
            parentContext = this,
            onBatteryComponentClick = { style ->
                Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Battery component selected: ${style.name}")
                viewModel.handleEvent(CustomizeEvent.SelectBatteryStyle(style))
            },
            onPremiumUnlock = { style ->
                Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Premium unlock requested for battery component: ${style.name}")
                // Handle premium unlock if needed
            }
        )

        emojiComponentAdapter = EmojiComponentAdapter(
            parentContext = this,
            onEmojiComponentClick = { style ->
                Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Emoji component selected: ${style.name}")
                viewModel.handleEvent(CustomizeEvent.SelectEmojiStyle(style))
            },
            onPremiumUnlock = { style ->
                Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Premium unlock requested for emoji component: ${style.name}")
                // Handle premium unlock if needed
            }
        )

        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Specialized adapters initialized successfully")
    }
    
    /**
     * Sets up RecyclerViews for style selection using specialized component adapters
     */
    private fun setupRecyclerViews() {
        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Setting up RecyclerViews with specialized adapters")

        // Battery components RecyclerView - displays only battery container images
        binding.batteryStylesRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@EmojiCustomizeActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = batteryComponentAdapter
            Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Battery component RecyclerView configured")
        }

        // Emoji components RecyclerView - displays only emoji character images
        binding.emojiStylesRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@EmojiCustomizeActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = emojiComponentAdapter
            Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Emoji component RecyclerView configured")
        }

        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: RecyclerViews setup completed with specialized adapters")
    }
    
    /**
     * Sets up click listeners for UI components
     */
    private fun setupClickListeners() {
        // Info button
        binding.customizeInfo.setOnClickListener {
            Log.d(TAG, "Info button clicked")
            showInfoDialog()
        }
        
        // Global toggle switch
        binding.globalToggleSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "TOGGLE_DEBUG: Global toggle switch clicked in Activity - isChecked=$isChecked")
            Log.d(TAG, "TOGGLE_DEBUG: Current ViewModel state - isGlobalEnabled=${viewModel.uiState.value.isGlobalEnabled}")
            Log.d(TAG, "TOGGLE_DEBUG: Sending ToggleGlobalEnabled event to ViewModel")
            viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(isChecked))
            Log.d(TAG, "TOGGLE_DEBUG: ToggleGlobalEnabled event sent")
        }
        
        // Toggle switches
        binding.showEmojiSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "Show emoji toggle changed: $isChecked")
            viewModel.handleEvent(CustomizeEvent.ToggleShowEmoji(isChecked))
        }
        
        binding.showPercentageSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "Show percentage toggle changed: $isChecked")
            viewModel.handleEvent(CustomizeEvent.ToggleShowPercentage(isChecked))
        }
        
        // Apply button
        binding.applyButton.setOnClickListener {
            Log.d(TAG, "Apply button clicked")
            viewModel.handleEvent(CustomizeEvent.ApplyCustomization)
        }
        
        // Retry button
        binding.retryButton.setOnClickListener {
            Log.d(TAG, "Retry button clicked")
            viewModel.handleEvent(CustomizeEvent.RetryLoad)
        }
    }
    
    /**
     * Shows info dialog explaining the customization features
     */
    private fun showInfoDialog() {
        // TODO: Implement info dialog
        Log.d(TAG, "Info dialog would be shown here")
    }

    /**
     * Sets up sliders for customization controls
     */
    private fun setupSliders() {
        Log.d(TAG, "Setting up sliders")

        // Font size slider (5dp to 40dp)
        binding.fontSizeSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val fontSize = progress + 5 // Convert 0-35 to 5-40
                    binding.fontSizeValue.text = "${fontSize}dp"
                    viewModel.handleEvent(CustomizeEvent.UpdatePercentageFontSize(fontSize))
                    Log.d(TAG, "Font size changed to: ${fontSize}dp")
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // Emoji scale slider (0.5x to 2.0x)
        binding.emojiScaleSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val scale = 0.5f + (progress * 0.1f) // Convert 0-15 to 0.5-2.0
                    binding.emojiScaleValue.text = String.format("%.1fx", scale)
                    viewModel.handleEvent(CustomizeEvent.UpdateEmojiSizeScale(scale))
                    Log.d(TAG, "Emoji scale changed to: ${scale}x")
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // Preview level slider
        binding.previewLevelSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    binding.previewLevelText.text = "${progress}%"
                    viewModel.handleEvent(CustomizeEvent.UpdatePreviewBatteryLevel(progress))
                    Log.d(TAG, "Preview level changed to: ${progress}%")
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }

    /**
     * Observes ViewModel state changes
     */
    private fun observeViewModel() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect { state ->
                    Log.d(TAG, "UI state updated: loading=${state.isLoading}, selectedStyle=${state.selectedStyle?.name}")
                    updateUI(state)
                }
            }
        }
    }

    /**
     * Updates the UI based on the current state
     */
    private fun updateUI(state: CustomizeState) {
        try {
            updateLoadingState(state)
            updateErrorState(state)
            updatePreview(state)
            updateStyleSelections(state)
            updateCustomizationControls(state)
            updateApplyButton(state)
            handleNavigation(state)
            handlePermissionRequest(state)
        } catch (exception: Exception) {
            Log.e(TAG, "Error updating UI", exception)
        }
    }

    /**
     * Updates loading state visibility
     */
    private fun updateLoadingState(state: CustomizeState) {
        binding.loadingContainer.visibility = if (state.isLoading) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }

        // Hide main content while loading
        binding.scrollView.visibility = if (state.isLoading) {
            android.view.View.GONE
        } else {
            android.view.View.VISIBLE
        }
    }

    /**
     * Updates error state visibility and message
     */
    private fun updateErrorState(state: CustomizeState) {
        binding.errorContainer.visibility = if (state.hasError) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }

        if (state.hasError) {
            binding.errorMessage.text = state.errorMessage.ifEmpty {
                getString(R.string.error_loading_customization)
            }
        }

        // Hide main content when showing error
        binding.scrollView.visibility = if (state.hasError) {
            android.view.View.GONE
        } else {
            android.view.View.VISIBLE
        }
    }

    /**
     * Updates the live preview based on current state
     */
    private fun updatePreview(state: CustomizeState) {
        // Update preview percentage text
        binding.previewPercentage.apply {
            text = "${state.previewBatteryLevel}%"
            visibility = if (state.showPercentageToggle) {
                android.view.View.VISIBLE
            } else {
                android.view.View.GONE
            }
        }

        // Update emoji visibility
        binding.previewEmoji.visibility = if (state.showEmojiToggle) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }

        // Load images using composite state for true mix-and-match
        loadCompositePreviewImages(state)
    }

    /**
     * Loads preview images using composite state for true mix-and-match functionality.
     * Phase 2: Uses independent battery and emoji selections.
     */
    private fun loadCompositePreviewImages(state: CustomizeState) {
        Log.d(TAG, "PHASE_2: Loading composite preview images")
        
        // Add debugging for ImageView dimensions
        binding.previewBattery.post {
            Log.d(TAG, "PREVIEW_DEBUG: Battery ImageView - Width: ${binding.previewBattery.width}, Height: ${binding.previewBattery.height}")
            Log.d(TAG, "PREVIEW_DEBUG: Container - Width: ${binding.previewContainer.width}, Height: ${binding.previewContainer.height}")
        }
        
        // Load battery image from composite state
        state.selectedBatteryStyle?.let { batteryStyle ->
            val batteryImageUrl = batteryStyle.batteryImageUrl
            if (batteryImageUrl.isNotEmpty()) {
                Log.d(TAG, "PHASE_2: Loading battery image from: $batteryImageUrl")
                Glide.with(this)
                    .load(batteryImageUrl)
                    .override(120, 120) // Force a minimum size
                    .centerInside()
                    .placeholder(R.drawable.ic_battery_placeholder)
                    .error(R.drawable.ic_battery_placeholder)
                    .listener(object : RequestListener<Drawable> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Drawable>,
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.w(TAG, "PHASE_2: Failed to load battery image: $batteryImageUrl", e)
                            return false
                        }

                        override fun onResourceReady(
                            resource: Drawable,
                            model: Any,
                            target: Target<Drawable>,
                            dataSource: DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.d(TAG, "PHASE_2: Battery image loaded successfully: ${resource.intrinsicWidth}x${resource.intrinsicHeight}")
                            return false
                        }
                    })
                    .into(binding.previewBattery)
            } else {
                Log.w(TAG, "PHASE_2: Battery style has empty image URL")
                Glide.with(this)
                    .load(R.drawable.ic_battery_placeholder)
                    .override(120, 120)
                    .centerInside()
                    .into(binding.previewBattery)
            }
        } ?: run {
            Log.w(TAG, "PHASE_2: No battery style selected, using placeholder")
            Glide.with(this)
                .load(R.drawable.ic_battery_placeholder)
                .override(120, 120)
                .centerInside()
                .into(binding.previewBattery)
        }

        // Load emoji image from composite state
        state.selectedEmojiStyle?.let { emojiStyle ->
            val emojiImageUrl = emojiStyle.emojiImageUrl
            if (emojiImageUrl.isNotEmpty()) {
                Log.d(TAG, "PHASE_2: Loading emoji image from: $emojiImageUrl")
                Glide.with(this)
                    .load(emojiImageUrl)
                    .override(120, 120) // Force a minimum size
                    .centerInside()
                    .placeholder(R.drawable.ic_emoji_placeholder)
                    .error(R.drawable.ic_emoji_placeholder)
                    .listener(object : RequestListener<Drawable> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Drawable>,
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.w(TAG, "PHASE_2: Failed to load emoji image: $emojiImageUrl", e)
                            return false
                        }

                        override fun onResourceReady(
                            resource: Drawable,
                            model: Any,
                            target: Target<Drawable>,
                            dataSource: DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.d(TAG, "PHASE_2: Emoji image loaded successfully: ${resource.intrinsicWidth}x${resource.intrinsicHeight}")
                            return false
                        }
                    })
                    .into(binding.previewEmoji)
            } else {
                Log.w(TAG, "PHASE_2: Emoji style has empty image URL")
                Glide.with(this)
                    .load(R.drawable.ic_emoji_placeholder)
                    .override(120, 120)
                    .centerInside()
                    .into(binding.previewEmoji)
            }
        } ?: run {
            Log.w(TAG, "PHASE_2: No emoji style selected, using placeholder")
            Glide.with(this)
                .load(R.drawable.ic_emoji_placeholder)
                .override(120, 120)
                .centerInside()
                .into(binding.previewEmoji)
        }
    }

    /**
     * Legacy method - kept for backward compatibility during transition.
     * @deprecated Use loadCompositePreviewImages instead for Phase 2 functionality.
     */
    @Deprecated("Use loadCompositePreviewImages for true mix-and-match functionality")
    private fun loadPreviewImages(style: BatteryStyle) {
        // Load emoji image
        if (style.emojiImageUrl.isNotEmpty()) {
            Glide.with(this)
                .load(style.emojiImageUrl)
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.w(TAG, "Failed to load emoji image: ${style.emojiImageUrl}", e)
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable,
                        model: Any,
                        target: Target<Drawable>,
                        dataSource: com.bumptech.glide.load.DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.d(TAG, "Emoji image loaded successfully: ${style.emojiImageUrl}")
                        return false
                    }
                })
                .into(binding.previewEmoji)
        }

        // Load battery image
        if (style.batteryImageUrl.isNotEmpty()) {
            Glide.with(this)
                .load(style.batteryImageUrl)
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.w(TAG, "Failed to load battery image: ${style.batteryImageUrl}", e)
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable,
                        model: Any,
                        target: Target<Drawable>,
                        dataSource: com.bumptech.glide.load.DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.d(TAG, "Battery image loaded successfully: ${style.batteryImageUrl}")
                        return false
                    }
                })
                .into(binding.previewBattery)
        }
    }

    /**
     * Updates style selection RecyclerViews using specialized component adapters
     */
    private fun updateStyleSelections(state: CustomizeState) {
        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Updating style selections")
        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Battery styles count: ${state.availableBatteryStyles.size}")
        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Emoji styles count: ${state.availableEmojiStyles.size}")

        // Update battery component carousel
        if (state.availableBatteryStyles.isNotEmpty()) {
            Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Submitting ${state.availableBatteryStyles.size} battery styles to component adapter")
            batteryComponentAdapter.submitList(state.availableBatteryStyles)
        } else {
            Log.w(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: ===== NO BATTERY STYLES AVAILABLE =====")
        }

        // Update emoji component carousel
        if (state.availableEmojiStyles.isNotEmpty()) {
            Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Submitting ${state.availableEmojiStyles.size} emoji styles to component adapter")
            emojiComponentAdapter.submitList(state.availableEmojiStyles)
        } else {
            Log.w(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: ===== NO EMOJI STYLES AVAILABLE =====")
        }

        // Update selection states in adapters
        batteryComponentAdapter.updateSelection(state.selectedBatteryStyle?.id)
        emojiComponentAdapter.updateSelection(state.selectedEmojiStyle?.id)

        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Style selections updated successfully")
    }

    /**
     * Updates customization controls based on state
     */
    private fun updateCustomizationControls(state: CustomizeState) {
        Log.d(TAG, "TOGGLE_DEBUG: updateCustomizationControls called in Activity")
        Log.d(TAG, "TOGGLE_DEBUG: State values - isGlobalEnabled=${state.isGlobalEnabled}, shouldRequestPermissions=${state.shouldRequestPermissions}")

        // Update global toggle without triggering listener
        Log.d(TAG, "TOGGLE_DEBUG: Removing listener from global toggle switch")
        binding.globalToggleSwitch.setOnCheckedChangeListener(null)

        Log.d(TAG, "TOGGLE_DEBUG: Setting global toggle switch checked state to ${state.isGlobalEnabled}")
        binding.globalToggleSwitch.isChecked = state.isGlobalEnabled

        Log.d(TAG, "TOGGLE_DEBUG: Re-attaching listener to global toggle switch")
        binding.globalToggleSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "TOGGLE_DEBUG: Global toggle switch listener triggered in Activity - isChecked=$isChecked")
            viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(isChecked))
        }

        // Update toggles without triggering listeners
        binding.showEmojiSwitch.setOnCheckedChangeListener(null)
        binding.showEmojiSwitch.isChecked = state.showEmojiToggle
        binding.showEmojiSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handleEvent(CustomizeEvent.ToggleShowEmoji(isChecked))
        }

        binding.showPercentageSwitch.setOnCheckedChangeListener(null)
        binding.showPercentageSwitch.isChecked = state.showPercentageToggle
        binding.showPercentageSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handleEvent(CustomizeEvent.ToggleShowPercentage(isChecked))
        }

        // Update sliders without triggering listeners
        binding.fontSizeSlider.setOnSeekBarChangeListener(null)
        binding.fontSizeSlider.progress = state.percentageFontSize - 5 // Convert 5-40 to 0-35
        binding.fontSizeValue.text = "${state.percentageFontSize}dp"
        setupSliders() // Re-attach listeners

        binding.emojiScaleSlider.setOnSeekBarChangeListener(null)
        binding.emojiScaleSlider.progress = ((state.emojiSizeScale - 0.5f) * 10).toInt() // Convert 0.5-2.0 to 0-15
        binding.emojiScaleValue.text = String.format("%.1fx", state.emojiSizeScale)
        setupSliders() // Re-attach listeners
    }

    /**
     * Updates apply button state and appearance
     */
    private fun updateApplyButton(state: CustomizeState) {
        binding.applyButton.apply {
            isEnabled = !state.isSaving && !state.isLoading && !state.hasError
            text = if (state.isSaving) {
                getString(R.string.applying_customization)
            } else {
                getString(R.string.apply_customization)
            }

            // Apply theme-aware button styling
            val currentTheme = ThemeManager.getSelectedTheme()
            val selectedColor = ThemeManager.getSelectedColor()

            // Set button background based on current theme and color
            when (selectedColor) {
                "blue" -> setBackgroundColor(getColor(R.color.blue))
                "green" -> setBackgroundColor(getColor(R.color.green))
                "orange" -> setBackgroundColor(getColor(R.color.orange))
                "pink" -> setBackgroundColor(getColor(R.color.pink))
                "red" -> setBackgroundColor(getColor(R.color.red))
                else -> setBackgroundColor(getColor(R.color.blue)) // Default
            }

            // Set text color based on theme
            val isDarkTheme = when (currentTheme) {
                "AutoTheme" -> {
                    val mode = resources.configuration.uiMode and
                        android.content.res.Configuration.UI_MODE_NIGHT_MASK
                    mode == android.content.res.Configuration.UI_MODE_NIGHT_YES
                }
                "BlackTheme", "BlackThemeInverted", "AmoledTheme", "AmoledThemeInverted" -> true
                else -> false
            }

            setTextColor(getColor(android.R.color.white)) // Always white text on colored button
        }
    }

    /**
     * Handles navigation events from the ViewModel
     */
    private fun handleNavigation(state: CustomizeState) {
        // Log navigation state for debugging but don't finish here
        // Back navigation is now handled immediately in handleBackNavigation()
        if (state.shouldNavigateBack) {
            Log.d(TAG, "EMOJI_NAVIGATION: ViewModel shouldNavigateBack=true (handled by direct finish)")
        }

        // Handle successful application - only this case needs ViewModel-triggered finish
        if (state.isCustomizationApplied && !isFinishing) {
            Log.d(TAG, "EMOJI_NAVIGATION: Customization applied successfully, finishing activity")
            // Clear the navigation state to prevent multiple calls
            viewModel.handleEvent(CustomizeEvent.ClearNavigationState)
            finish()
        }
    }

    /**
     * Handles permission request when shouldRequestPermissions is true
     */
    private fun handlePermissionRequest(state: CustomizeState) {
        Log.d(TAG, "TOGGLE_DEBUG: handlePermissionRequest called in Activity - shouldRequestPermissions=${state.shouldRequestPermissions}")

        if (state.shouldRequestPermissions) {
            Log.d(TAG, "TOGGLE_DEBUG: Permission request triggered in Activity - showing permission dialog")
            Log.d(TAG, "EMOJI_PERMISSION: Permission request triggered")

            EmojiOverlayPermissionManager.checkPermissionsAndProceed(
                context = this,
                onAllPermissionsGranted = {
                    Log.d(TAG, "TOGGLE_DEBUG: Permission dialog result in Activity - ALL PERMISSIONS GRANTED")
                    Log.d(TAG, "EMOJI_PERMISSION: All permissions granted, enabling feature")
                    viewModel.handlePermissionResult(granted = true)
                },
                onPermissionsDenied = {
                    Log.d(TAG, "TOGGLE_DEBUG: Permission dialog result in Activity - PERMISSIONS DENIED")
                    Log.d(TAG, "EMOJI_PERMISSION: Permissions denied by user")
                    viewModel.handlePermissionResult(granted = false)
                },
                showExplanation = true
            )
        } else {
            Log.d(TAG, "TOGGLE_DEBUG: No permission request needed in Activity")
        }
    }
}
