package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.view.MotionEvent
import com.tqhit.battery.one.utils.BatteryLogger
import kotlin.math.abs

/**
 * Helper class for detecting and processing emoji overlay gestures.
 * Provides advanced gesture detection with left/right half screen detection
 * for different notification actions as specified in Task 4.2.
 * 
 * This class follows clean architecture principles:
 * - Pure business logic without Android framework dependencies
 * - Testable with unit tests using mock MotionEvent objects
 * - Single responsibility for gesture detection and classification
 * - Immutable data structures for gesture results
 */
class EmojiGestureHelper {
    
    companion object {
        private const val TAG = "EmojiGestureHelper"
        private const val EMOJI_GESTURE_TAG = "EmojiGesture_Detection"
        
        // Gesture detection thresholds
        private const val MIN_SWIPE_DISTANCE = 100f
        private const val MIN_VELOCITY_THRESHOLD = 200f
        private const val VERTICAL_DOMINANCE_RATIO = 2f // deltaY must be 2x deltaX for vertical swipe
    }
    
    /**
     * Enum representing the type of gesture detected.
     */
    enum class GestureType {
        NONE,
        SWIPE_DOWN_LEFT,   // Swipe down on left half - opens notifications
        SWIPE_DOWN_RIGHT,  // Swipe down on right half - opens quick settings
        SWIPE_DOWN_CENTER  // Swipe down in center - default behavior
    }
    
    /**
     * Data class representing the result of gesture detection.
     * 
     * @param gestureType The type of gesture detected
     * @param startX X coordinate where the gesture started
     * @param startY Y coordinate where the gesture started
     * @param endX X coordinate where the gesture ended
     * @param endY Y coordinate where the gesture ended
     * @param deltaX Horizontal distance of the gesture
     * @param deltaY Vertical distance of the gesture
     * @param velocityX Horizontal velocity of the gesture
     * @param velocityY Vertical velocity of the gesture
     * @param screenWidth Width of the screen for position calculations
     */
    data class GestureResult(
        val gestureType: GestureType,
        val startX: Float,
        val startY: Float,
        val endX: Float,
        val endY: Float,
        val deltaX: Float,
        val deltaY: Float,
        val velocityX: Float,
        val velocityY: Float,
        val screenWidth: Int
    ) {
        /**
         * Determines if the gesture started on the left half of the screen.
         */
        val isLeftHalf: Boolean
            get() = startX < (screenWidth / 2f)
        
        /**
         * Determines if the gesture started on the right half of the screen.
         */
        val isRightHalf: Boolean
            get() = startX >= (screenWidth / 2f)
        
        /**
         * Gets a human-readable description of the gesture for logging.
         */
        val description: String
            get() = "GestureResult(type=$gestureType, start=($startX,$startY), end=($endX,$endY), " +
                   "delta=($deltaX,$deltaY), velocity=($velocityX,$velocityY), " +
                   "screenWidth=$screenWidth, leftHalf=$isLeftHalf)"
    }
    
    /**
     * Analyzes a fling gesture and determines the appropriate action.
     * 
     * @param startEvent The initial motion event (ACTION_DOWN)
     * @param endEvent The final motion event (ACTION_UP)
     * @param velocityX Horizontal velocity in pixels per second
     * @param velocityY Vertical velocity in pixels per second
     * @param screenWidth Width of the screen for position calculations
     * @return GestureResult containing the detected gesture type and details
     */
    fun analyzeGesture(
        startEvent: MotionEvent?,
        endEvent: MotionEvent?,
        velocityX: Float,
        velocityY: Float,
        screenWidth: Int
    ): GestureResult {
        BatteryLogger.d(EMOJI_GESTURE_TAG, "GESTURE_ANALYSIS_STARTED")
        
        // Validate input events
        if (startEvent == null || endEvent == null) {
            BatteryLogger.w(EMOJI_GESTURE_TAG, "Invalid gesture events - start or end event is null")
            return createNoGestureResult(screenWidth)
        }
        
        val startX = startEvent.x
        val startY = startEvent.y
        val endX = endEvent.x
        val endY = endEvent.y
        val deltaX = endX - startX
        val deltaY = endY - startY
        
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Gesture coordinates: start=($startX,$startY), end=($endX,$endY)")
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Gesture deltas: deltaX=$deltaX, deltaY=$deltaY")
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Gesture velocities: velocityX=$velocityX, velocityY=$velocityY")
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Screen width: $screenWidth")
        
        // Check if this is a valid downward swipe
        val gestureType = determineGestureType(startX, deltaX, deltaY, velocityY, screenWidth)
        
        val result = GestureResult(
            gestureType = gestureType,
            startX = startX,
            startY = startY,
            endX = endX,
            endY = endY,
            deltaX = deltaX,
            deltaY = deltaY,
            velocityX = velocityX,
            velocityY = velocityY,
            screenWidth = screenWidth
        )
        
        BatteryLogger.d(EMOJI_GESTURE_TAG, "GESTURE_ANALYSIS_COMPLETED: ${result.description}")
        return result
    }
    
    /**
     * Determines the gesture type based on movement and position.
     */
    private fun determineGestureType(
        startX: Float,
        deltaX: Float,
        deltaY: Float,
        velocityY: Float,
        screenWidth: Int
    ): GestureType {
        BatteryLogger.d(EMOJI_GESTURE_TAG, "GESTURE_TYPE_DETERMINATION_STARTED")
        
        // Check if it's a valid downward swipe
        if (!isValidDownwardSwipe(deltaX, deltaY, velocityY)) {
            BatteryLogger.d(EMOJI_GESTURE_TAG, "Not a valid downward swipe - returning NONE")
            return GestureType.NONE
        }
        
        // Determine position on screen
        val screenHalf = screenWidth / 2f
        val leftThreshold = screenWidth * 0.3f  // 30% from left
        val rightThreshold = screenWidth * 0.7f // 70% from left
        
        val gestureType = when {
            startX < leftThreshold -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Gesture in left zone (x=$startX < $leftThreshold)")
                GestureType.SWIPE_DOWN_LEFT
            }
            startX > rightThreshold -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Gesture in right zone (x=$startX > $rightThreshold)")
                GestureType.SWIPE_DOWN_RIGHT
            }
            else -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Gesture in center zone (x=$startX between $leftThreshold and $rightThreshold)")
                GestureType.SWIPE_DOWN_CENTER
            }
        }
        
        BatteryLogger.d(EMOJI_GESTURE_TAG, "GESTURE_TYPE_DETERMINED: $gestureType")
        return gestureType
    }
    
    /**
     * Validates if the gesture qualifies as a downward swipe.
     */
    private fun isValidDownwardSwipe(deltaX: Float, deltaY: Float, velocityY: Float): Boolean {
        val isDownward = deltaY > MIN_SWIPE_DISTANCE
        val hasVerticalDominance = abs(deltaY) > abs(deltaX) * VERTICAL_DOMINANCE_RATIO
        val hasMinimumVelocity = abs(velocityY) > MIN_VELOCITY_THRESHOLD
        
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Swipe validation: downward=$isDownward (deltaY=$deltaY > $MIN_SWIPE_DISTANCE)")
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Swipe validation: verticalDominance=$hasVerticalDominance (|$deltaY| > |$deltaX| * $VERTICAL_DOMINANCE_RATIO)")
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Swipe validation: minimumVelocity=$hasMinimumVelocity (|$velocityY| > $MIN_VELOCITY_THRESHOLD)")
        
        val isValid = isDownward && hasVerticalDominance && hasMinimumVelocity
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Swipe validation result: $isValid")
        
        return isValid
    }
    
    /**
     * Creates a result indicating no gesture was detected.
     */
    private fun createNoGestureResult(screenWidth: Int): GestureResult {
        return GestureResult(
            gestureType = GestureType.NONE,
            startX = 0f,
            startY = 0f,
            endX = 0f,
            endY = 0f,
            deltaX = 0f,
            deltaY = 0f,
            velocityX = 0f,
            velocityY = 0f,
            screenWidth = screenWidth
        )
    }
    
    /**
     * Gets the appropriate accessibility action for the gesture type.
     * 
     * @param gestureType The detected gesture type
     * @return The accessibility action constant or null if no action needed
     */
    fun getAccessibilityActionForGesture(gestureType: GestureType): Int? {
        return when (gestureType) {
            GestureType.SWIPE_DOWN_LEFT -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Mapping left swipe to GLOBAL_ACTION_NOTIFICATIONS")
                AccessibilityService.GLOBAL_ACTION_NOTIFICATIONS
            }
            GestureType.SWIPE_DOWN_RIGHT -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Mapping right swipe to GLOBAL_ACTION_QUICK_SETTINGS")
                AccessibilityService.GLOBAL_ACTION_QUICK_SETTINGS
            }
            GestureType.SWIPE_DOWN_CENTER -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Mapping center swipe to GLOBAL_ACTION_NOTIFICATIONS (default)")
                AccessibilityService.GLOBAL_ACTION_NOTIFICATIONS
            }
            GestureType.NONE -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "No gesture detected - no accessibility action")
                null
            }
        }
    }
}
