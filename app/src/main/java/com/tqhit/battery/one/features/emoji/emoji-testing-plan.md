# Emoji Battery Feature: Testing Plan

**Author:** Gemini
**Date:** July 6, 2025
**Status:** Initial Draft

## 1. Overview

This document outlines the comprehensive testing strategy for the Emoji Battery feature, focusing on robust unit and instrumentation tests to ensure quality and prevent regressions. The primary challenge is testing UI components and services that rely on the Android framework and external dependencies like Firebase.

Our strategy is to use a combination of **Robolectric** for framework-aware unit tests and **Espresso** for on-device instrumentation tests.

## 2. Tools and Frameworks

- **JUnit 4/5:** The core testing framework.
- **Robolectric:** For running Android framework-dependent tests on the JVM. It simulates the Android environment, allowing access to resources, themes, and views without an emulator.
- **Mockito/MockK:** For mocking dependencies and verifying interactions.
- **HiltAndroidTest:** For testing Hilt dependency injection.
- **Turbine:** For testing Kotlin Flows.
- **Espresso:** For UI instrumentation tests that run on a device or emulator.

## 3. Unit Testing Strategy (Phase 4)

### Task 4.1: `EmojiBatteryView.kt` Unit Tests

**Problem:** The current tests fail because they use a mock `Context`, but the view requires a real context to access resources (`R.color`, `R.dimen`, themes).

**Solution:** Use Robolectric to provide a real `ApplicationProvider.getApplicationContext()`.

**Updated Test Implementation (`EmojiBatteryViewTest.kt`):**

```kotlin
import android.graphics.Color
import androidx.core.content.ContextCompat
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.shadows.ShadowPaint

@RunWith(AndroidJUnit4::class)
class EmojiBatteryViewTest {

    private lateinit var emojiBatteryView: EmojiBatteryView

    @Before
    fun setUp() {
        // Use the application context provided by Robolectric
        val context = ApplicationProvider.getApplicationContext<Context>()
        // Set a theme to avoid theme-related crashes
        context.setTheme(R.style.Theme_MaterialComponents_DayNight) 
        emojiBatteryView = EmojiBatteryView(context)
    }

    @Test
    fun `updateCustomizationConfig should apply color and size correctly`() {
        // Arrange
        val testColor = Color.RED
        val testFontSize = 20f
        val config = CustomizationConfig.createDefault().copy(
            customConfig = CustomizationConfig.createDefault().customConfig.copy(
                percentageColor = testColor,
                percentageFontSizeDp = testFontSize.toInt()
            )
        )

        // Act
        emojiBatteryView.updateCustomizationConfig(config)
        
        // Assert
        // We need a way to verify the internal state. Let's assume we add a getter for the paint object for testing.
        // Or, we can use Robolectric shadows if the paint object is not accessible.
        // For now, let's focus on what we can test without modifying the source.
        // A better approach would be to use screenshot testing with a library like Paparazzi or Roborazzi.
        
        // This test now passes because the view can be created successfully.
        // More specific assertions are needed.
    }
    
    // ... other tests ...
}
```

### Task 4.2: `EmojiBatteryAccessibilityService.kt` Unit Tests

**Problem:** Testing an `AccessibilityService` is complex due to its lifecycle and interaction with the `WindowManager`.

**Solution:** Use Robolectric's `ServiceController` and a mock `WindowManager`.

**Test Implementation (`EmojiBatteryAccessibilityServiceTest.kt`):**

```kotlin
// In the service test file
@RunWith(AndroidJUnit4::class)
class EmojiBatteryAccessibilityServiceTest {

    private lateinit var serviceController: ServiceController<EmojiBatteryAccessibilityService>
    private lateinit var service: EmojiBatteryAccessibilityService
    
    @Mock
    private lateinit var mockWindowManager: WindowManager

    @Before
    fun setUp() {
        // Mock the WindowManager
        val context = ApplicationProvider.getApplicationContext<Context>()
        context.getSystemService(Context.WINDOW_SERVICE)
        // ... setup mockWindowManager ...

        serviceController = Robolectric.buildService(EmojiBatteryAccessibilityService::class.java)
        service = serviceController.get()
        
        // Inject mocks if using Hilt
    }

    @Test
    fun `onServiceConnected should add view to window manager`() {
        // Act
        serviceController.create().get().onServiceConnected()

        // Assert
        // verify(mockWindowManager).addView(any(), any())
    }
}
```

## 4. Next Steps

1.  **Apply the Fix:** Update the `EmojiBatteryViewTest.kt` to use Robolectric as shown above.
2.  **Add Dependencies:** Ensure `build.gradle.kts` has the necessary Robolectric and other testing dependencies.
3.  **Run Tests:** Re-run the tests to confirm they pass.
4.  **Expand Assertions:** Improve the tests to include more specific assertions about the view's state after updates. Consider screenshot testing for visual verification.
5.  **Follow this Plan:** Use this document as a guide for writing tests for the rest of the feature.
