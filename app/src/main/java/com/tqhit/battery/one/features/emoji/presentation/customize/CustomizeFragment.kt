package com.tqhit.battery.one.features.emoji.presentation.customize

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.SeekBar
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.FragmentEmojiCustomizeBinding
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiOverlayPermissionManager
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject
import android.graphics.drawable.Drawable

/**
 * Fragment for customizing emoji battery styles.
 * Provides live preview and customization options for selected battery styles.
 * 
 * This fragment follows the established patterns in the app:
 * - Extends AdLibBaseFragment for ad integration
 * - Uses ViewBinding for view access
 * - Implements MVI pattern with ViewModel
 * - Uses Hilt for dependency injection
 * - Follows Material 3 design guidelines
 */
@AndroidEntryPoint
class CustomizeFragment : AdLibBaseFragment<FragmentEmojiCustomizeBinding>() {
    
    companion object {
        private const val TAG = "CustomizeFragment"
        private const val ARG_BATTERY_STYLE = "battery_style"
        
        /**
         * Creates a new instance of CustomizeFragment with a battery style.
         */
        fun newInstance(batteryStyle: BatteryStyle): CustomizeFragment {
            return CustomizeFragment().apply {
                arguments = Bundle().apply {
                    putSerializable(ARG_BATTERY_STYLE, batteryStyle)
                }
            }
        }
    }
    
    // ViewBinding
    override val binding by lazy { FragmentEmojiCustomizeBinding.inflate(layoutInflater) }
    
    // ViewModel
    private val viewModel: CustomizeViewModel by viewModels()
    
    // Dependencies
    @Inject
    lateinit var appRepository: AppRepository
    
    // Adapters
    private lateinit var batteryStyleAdapter: BatteryStyleAdapter
    private lateinit var emojiStyleAdapter: BatteryStyleAdapter
    
    // State
    private var initialBatteryStyle: BatteryStyle? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Get the battery style from arguments
        initialBatteryStyle = arguments?.getSerializable(ARG_BATTERY_STYLE) as? BatteryStyle
        Log.d(TAG, "CustomizeFragment created with style: ${initialBatteryStyle?.name}")
    }
    
    override fun setupUI() {
        super.setupUI()
        Log.d(TAG, "CustomizeFragment setupUI called")

        setupUIComponents()
        setupRecyclerViews()
        setupClickListeners()
        setupSliders()
        observeViewModel()

        // Initialize with the provided battery style or load initial data
        initialBatteryStyle?.let { style ->
            viewModel.handleEvent(CustomizeEvent.InitializeWithStyle(style))
        } ?: run {
            viewModel.handleEvent(CustomizeEvent.LoadInitialData)
        }
    }
    
    override fun onResume() {
        super.onResume()
        viewModel.handleEvent(CustomizeEvent.OnResume)

        // Check if permissions were granted while away from the app
        if (EmojiOverlayPermissionManager.hasAllRequiredPermissions(requireContext())) {
            Log.d(TAG, "EMOJI_PERMISSION: Permissions now granted, enabling feature")
            viewModel.handlePermissionResult(granted = true)
        } else {
            Log.d(TAG, "EMOJI_PERMISSION: Permissions still missing")
            // Permission status will be logged by the permission manager
            val permissionStatus = EmojiOverlayPermissionManager.getPermissionStatusSummary(requireContext())
            Log.d(TAG, "EMOJI_PERMISSION: Current permission status: $permissionStatus")
        }
    }
    
    override fun onPause() {
        super.onPause()
        viewModel.handleEvent(CustomizeEvent.OnPause)
    }
    
    /**
     * Sets up the UI components
     */
    private fun setupUIComponents() {
        try {
            // Setup banner ad
            setupBannerAd()
            
            Log.d(TAG, "UI components setup completed")
        } catch (exception: Exception) {
            Log.e(TAG, "Error setting up UI components", exception)
        }
    }
    
    /**
     * Sets up the RecyclerViews for battery and emoji styles
     */
    private fun setupRecyclerViews() {
        try {
            setupBatteryStyleRecyclerView()
            setupEmojiStyleRecyclerView()
            Log.d(TAG, "RecyclerViews setup completed")
        } catch (exception: Exception) {
            Log.e(TAG, "Error setting up RecyclerViews", exception)
        }
    }
    
    /**
     * Sets up the battery style RecyclerView
     */
    private fun setupBatteryStyleRecyclerView() {
        batteryStyleAdapter = BatteryStyleAdapter(
            requireActivity(),
            onStyleClick = { style ->
                Log.d(TAG, "Battery style selected: ${style.name}")
                viewModel.handleEvent(CustomizeEvent.SelectBatteryStyle(style))
            },
            onPremiumUnlock = { style ->
                Log.d(TAG, "Premium unlock requested for battery style: ${style.name}")
                // Handle premium unlock if needed
            }
        )
        
        binding.batteryStylesRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            adapter = batteryStyleAdapter
        }
    }
    
    /**
     * Sets up the emoji style RecyclerView
     */
    private fun setupEmojiStyleRecyclerView() {
        emojiStyleAdapter = BatteryStyleAdapter(
            requireActivity(),
            onStyleClick = { style ->
                Log.d(TAG, "Emoji style selected: ${style.name}")
                viewModel.handleEvent(CustomizeEvent.SelectEmojiStyle(style))
            },
            onPremiumUnlock = { style ->
                Log.d(TAG, "Premium unlock requested for emoji style: ${style.name}")
                // Handle premium unlock if needed
            }
        )
        
        binding.emojiStylesRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            adapter = emojiStyleAdapter
        }
    }
    
    /**
     * Sets up click listeners for UI components
     */
    private fun setupClickListeners() {
        // Back navigation
        binding.backNavigation.btnBackNavigation.setOnClickListener {
            Log.d(TAG, "Back navigation clicked")
            viewModel.handleEvent(CustomizeEvent.NavigateBack)
        }
        
        // Info button
        binding.customizeInfo.setOnClickListener {
            Log.d(TAG, "Info button clicked")
            showInfoDialog()
        }
        
        // Global toggle switch
        binding.globalToggleSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "TOGGLE_DEBUG: Global toggle switch clicked - isChecked=$isChecked")
            Log.d(TAG, "TOGGLE_DEBUG: Current ViewModel state - isGlobalEnabled=${viewModel.uiState.value.isGlobalEnabled}")
            Log.d(TAG, "TOGGLE_DEBUG: Sending ToggleGlobalEnabled event to ViewModel")
            viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(isChecked))
            Log.d(TAG, "TOGGLE_DEBUG: ToggleGlobalEnabled event sent")
        }
        
        // Toggle switches
        binding.showEmojiSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "Show emoji toggle changed: $isChecked")
            viewModel.handleEvent(CustomizeEvent.ToggleShowEmoji(isChecked))
        }
        
        binding.showPercentageSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "Show percentage toggle changed: $isChecked")
            viewModel.handleEvent(CustomizeEvent.ToggleShowPercentage(isChecked))
        }
        
        // Apply button
        binding.applyButton.setOnClickListener {
            Log.d(TAG, "Apply button clicked")
            viewModel.handleEvent(CustomizeEvent.ApplyCustomization)
        }
        
        // Retry button
        binding.retryButton.setOnClickListener {
            Log.d(TAG, "Retry button clicked")
            viewModel.handleEvent(CustomizeEvent.RetryLoad)
        }
    }
    
    /**
     * Sets up sliders and seek bars
     */
    private fun setupSliders() {
        // Preview level slider
        binding.previewLevelSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    Log.d(TAG, "Preview level changed: $progress")
                    viewModel.handleEvent(CustomizeEvent.UpdatePreviewBatteryLevel(progress))
                    binding.previewLevelText.text = "$progress%"
                }
            }
            
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
        
        // Font size slider (range: 5dp to 40dp, max=35, so progress + 5 = actual size)
        binding.fontSizeSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val fontSize = progress + 5 // Convert progress (0-35) to font size (5-40)
                    Log.d(TAG, "Font size changed: $fontSize")
                    viewModel.handleEvent(CustomizeEvent.UpdatePercentageFontSize(fontSize))
                    binding.fontSizeValue.text = "${fontSize}dp"
                }
            }
            
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
        
        // Emoji scale slider (range: 0.5x to 2.0x, max=15, so (progress * 0.1) + 0.5 = actual scale)
        binding.emojiScaleSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val scale = (progress * 0.1f) + 0.5f // Convert progress (0-15) to scale (0.5-2.0)
                    Log.d(TAG, "Emoji scale changed: $scale")
                    viewModel.handleEvent(CustomizeEvent.UpdateEmojiSizeScale(scale))
                    binding.emojiScaleValue.text = String.format("%.1fx", scale)
                }
            }
            
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }
    
    /**
     * Observes ViewModel state changes
     */
    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect { state ->
                    Log.d(TAG, "UI state updated: loading=${state.isLoading}, selectedStyle=${state.selectedStyle?.name}")
                    updateUI(state)
                }
            }
        }
    }
    
    /**
     * Updates the UI based on the current state
     */
    private fun updateUI(state: CustomizeState) {
        try {
            updateLoadingState(state)
            updateErrorState(state)
            updatePreview(state)
            updateStyleSelections(state)
            updateCustomizationControls(state)
            updateApplyButton(state)
            handleNavigation(state)
            handlePermissionRequest(state)
        } catch (exception: Exception) {
            Log.e(TAG, "Error updating UI", exception)
        }
    }
    
    /**
     * Updates loading state
     */
    private fun updateLoadingState(state: CustomizeState) {
        binding.loadingContainer.visibility = if (state.isLoading && !state.isInitialLoadComplete) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }
    
    /**
     * Updates error state
     */
    private fun updateErrorState(state: CustomizeState) {
        if (state.hasError) {
            binding.errorContainer.visibility = View.VISIBLE
            binding.errorMessage.text = state.errorMessage
        } else {
            binding.errorContainer.visibility = View.GONE
        }
    }
    
    /**
     * Updates the live preview
     */
    private fun updatePreview(state: CustomizeState) {
        if (state.canShowPreview) {
            // Update preview visibility based on toggles
            binding.previewEmoji.visibility = if (state.showEmojiToggle) View.VISIBLE else View.GONE
            binding.previewPercentage.visibility = if (state.showPercentageToggle) View.VISIBLE else View.GONE
            
            // Update preview content
            binding.previewPercentage.text = "${state.previewBatteryLevel}%"
            
            // Load images if available
            state.selectedStyle?.let { style ->
                loadPreviewImages(style)
            }
        }
        
        // Update preview level slider
        if (binding.previewLevelSlider.progress != state.previewBatteryLevel) {
            binding.previewLevelSlider.progress = state.previewBatteryLevel
            binding.previewLevelText.text = "${state.previewBatteryLevel}%"
        }
    }
    
    /**
     * Updates style selections
     */
    private fun updateStyleSelections(state: CustomizeState) {
        batteryStyleAdapter.submitList(state.availableBatteryStyles)
        emojiStyleAdapter.submitList(state.availableEmojiStyles)
    }
    
    /**
     * Updates customization controls
     */
    private fun updateCustomizationControls(state: CustomizeState) {
        Log.d(TAG, "TOGGLE_DEBUG: updateCustomizationControls called")
        Log.d(TAG, "TOGGLE_DEBUG: State values - isGlobalEnabled=${state.isGlobalEnabled}, shouldRequestPermissions=${state.shouldRequestPermissions}")

        // Update global toggle without triggering listener
        Log.d(TAG, "TOGGLE_DEBUG: Removing listener from global toggle switch")
        binding.globalToggleSwitch.setOnCheckedChangeListener(null)

        Log.d(TAG, "TOGGLE_DEBUG: Setting global toggle switch checked state to ${state.isGlobalEnabled}")
        binding.globalToggleSwitch.isChecked = state.isGlobalEnabled

        Log.d(TAG, "TOGGLE_DEBUG: Re-attaching listener to global toggle switch")
        binding.globalToggleSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "TOGGLE_DEBUG: Global toggle switch listener triggered - isChecked=$isChecked")
            viewModel.handleEvent(CustomizeEvent.ToggleGlobalEnabled(isChecked))
        }
        
        // Update toggles without triggering listeners
        binding.showEmojiSwitch.setOnCheckedChangeListener(null)
        binding.showEmojiSwitch.isChecked = state.showEmojiToggle
        binding.showEmojiSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handleEvent(CustomizeEvent.ToggleShowEmoji(isChecked))
        }
        
        binding.showPercentageSwitch.setOnCheckedChangeListener(null)
        binding.showPercentageSwitch.isChecked = state.showPercentageToggle
        binding.showPercentageSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handleEvent(CustomizeEvent.ToggleShowPercentage(isChecked))
        }
        
        // Update sliders without triggering listeners
        binding.fontSizeSlider.setOnSeekBarChangeListener(null)
        val fontSizeProgress = (state.percentageFontSize - 5).coerceIn(0, 35)
        binding.fontSizeSlider.progress = fontSizeProgress
        binding.fontSizeValue.text = "${state.percentageFontSize}dp"
        binding.fontSizeSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val fontSize = progress + 5
                    viewModel.handleEvent(CustomizeEvent.UpdatePercentageFontSize(fontSize))
                    binding.fontSizeValue.text = "${fontSize}dp"
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
        
        binding.emojiScaleSlider.setOnSeekBarChangeListener(null)
        val emojiScaleProgress = ((state.emojiSizeScale - 0.5f) * 10).toInt().coerceIn(0, 15)
        binding.emojiScaleSlider.progress = emojiScaleProgress
        binding.emojiScaleValue.text = String.format("%.1fx", state.emojiSizeScale)
        binding.emojiScaleSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val scale = (progress * 0.1f) + 0.5f
                    viewModel.handleEvent(CustomizeEvent.UpdateEmojiSizeScale(scale))
                    binding.emojiScaleValue.text = String.format("%.1fx", scale)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }
    
    /**
     * Updates apply button state
     */
    private fun updateApplyButton(state: CustomizeState) {
        binding.applyButton.isEnabled = state.canApplyChanges
        binding.applyButton.text = if (state.isSaving) {
            "Applying..."
        } else {
            getString(R.string.apply_customization)
        }
    }
    
    /**
     * Handles navigation events
     */
    private fun handleNavigation(state: CustomizeState) {
        if (state.shouldNavigateBack) {
            Log.d(TAG, "Navigating back to gallery")
            requireActivity().supportFragmentManager.popBackStack()
        }
        
        if (state.shouldShowSuccessMessage) {
            // TODO: Show success message
            Log.d(TAG, "Customization applied successfully")
        }
    }
    
    /**
     * Shows info dialog
     */
    private fun showInfoDialog() {
        // TODO: Implement info dialog
        Log.d(TAG, "Info dialog not yet implemented")
    }
    
    /**
     * Loads preview images using Glide with proper error handling
     */
    private fun loadPreviewImages(style: BatteryStyle) {
        try {
            // Load battery preview image
            val batteryImageUrl = style.customizePreviewUrl.ifBlank { style.batteryImageUrl }
            if (batteryImageUrl.isNotBlank()) {
                Log.d(TAG, "Loading battery preview image: $batteryImageUrl")
                Glide.with(this)
                    .load(batteryImageUrl)
                    .centerInside()
                    .placeholder(R.drawable.ic_battery_placeholder)
                    .error(R.drawable.ic_battery_placeholder)
                    .listener(object : RequestListener<Drawable> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Drawable>,
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.w(TAG, "Failed to load battery preview image: $batteryImageUrl", e)
                            return false
                        }

                        override fun onResourceReady(
                            resource: Drawable,
                            model: Any,
                            target: Target<Drawable>,
                            dataSource: com.bumptech.glide.load.DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.d(TAG, "Battery preview image loaded successfully")
                            return false
                        }
                    })
                    .into(binding.previewBattery)
            }
            
            // Load emoji preview image
            val emojiImageUrl = style.emojiImageUrl
            if (emojiImageUrl.isNotBlank()) {
                Log.d(TAG, "Loading emoji preview image: $emojiImageUrl")
                Glide.with(this)
                    .load(emojiImageUrl)
                    .centerInside()
                    .placeholder(R.drawable.ic_emoji_placeholder)
                    .error(R.drawable.ic_emoji_placeholder)
                    .listener(object : RequestListener<Drawable> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Drawable>,
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.w(TAG, "Failed to load emoji preview image: $emojiImageUrl", e)
                            return false
                        }

                        override fun onResourceReady(
                            resource: Drawable,
                            model: Any,
                            target: Target<Drawable>,
                            dataSource: com.bumptech.glide.load.DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.d(TAG, "Emoji preview image loaded successfully")
                            return false
                        }
                    })
                    .into(binding.previewEmoji)
            }
            
        } catch (exception: Exception) {
            Log.e(TAG, "Error loading preview images", exception)
        }
    }

    /**
     * Handles permission request when shouldRequestPermissions is true
     */
    private fun handlePermissionRequest(state: CustomizeState) {
        Log.d(TAG, "TOGGLE_DEBUG: handlePermissionRequest called - shouldRequestPermissions=${state.shouldRequestPermissions}")

        if (state.shouldRequestPermissions) {
            Log.d(TAG, "TOGGLE_DEBUG: Permission request triggered - showing permission dialog")
            Log.d(TAG, "EMOJI_PERMISSION: Permission request triggered")

            EmojiOverlayPermissionManager.checkPermissionsAndProceed(
                context = requireContext(),
                onAllPermissionsGranted = {
                    Log.d(TAG, "TOGGLE_DEBUG: Permission dialog result - ALL PERMISSIONS GRANTED")
                    Log.d(TAG, "EMOJI_PERMISSION: All permissions granted, enabling feature")
                    viewModel.handlePermissionResult(granted = true)
                },
                onPermissionsDenied = {
                    Log.d(TAG, "TOGGLE_DEBUG: Permission dialog result - PERMISSIONS DENIED")
                    Log.d(TAG, "EMOJI_PERMISSION: Permissions denied by user")
                    viewModel.handlePermissionResult(granted = false)
                },
                showExplanation = true
            )
        } else {
            Log.d(TAG, "TOGGLE_DEBUG: No permission request needed")
        }
    }

    /**
     * Sets up banner ad
     */
    private fun setupBannerAd() {
        try {
            // TODO: Integrate with ApplovinBannerAdManager
            Log.d(TAG, "Banner ad setup not yet implemented")
        } catch (exception: Exception) {
            Log.e(TAG, "Error setting up banner ad", exception)
        }
    }
}
